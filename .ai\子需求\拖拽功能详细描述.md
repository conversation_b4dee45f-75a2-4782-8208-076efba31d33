## 概述

## 核心功能详细描述

### 数据方面：
      - 组件列表中，每一个资产包含数据：is_viewed, icon, name这几个数据用于在组件列表中显示；其他背后数据沿用从素材库的数据；is_viewed需要给出接口，有统一管理的模块，后续用于maya api模块触发功能；
      - 组件列表会存储当前列表数据，需要支持后续的导出、导入json等数据记录功能；

### UI交互方面：
  - 素材库中选中一个资产时，需要高亮显示；
  - 只有鼠标释放时，该素材才会被显示到组件列表中；
  - 素材库和组件列表都是单选模式；

## 功能验证核心点
我希望你帮我创建一个测试文件，用于测试ui整体逻辑是否有报错，功能是否正常等；主要是模拟用户核心的ui交互；
1. 拖拽功能：
  操作动作模拟: 从hair asset library拖拽一个数据到hair component list中；
需要检测验证：1. component list中是否正确刷新了该数据，该数据条目是否属于选中状态；2. 选中状态后，毛发编辑区域是否刷新了数据的显示；
2. 毛发组件列表的删除功能： 动作模拟：选中毛发组件列表其中的一个item，点击删除按钮；
需要检测验证：1. 组件列表中该数据是否成功移除；2. 移除后，是否组件列表没有被选中的item了；对应的，是否毛发编辑区域数据跟随更新；
正确实现了该测试文件以后，总结如何使用，测试结果输出，记录报错bug等发生产生场景；然后根据测试结果，修复报错功能，同样给出总结

## 实现核心单元测试

### 验证测试用例环境


