# 毛发工作室拖放功能实现

## 概述

本文档描述了毛发工作室模块中的拖放功能实现，允许用户从资源库中拖拽资源到组件列表以创建新的毛发组件。

## 架构

### 涉及组件

1. **AssetItem** (`asset_item.py`) - 拖拽源
2. **ComponentList** (`component_list.py`) - 放置目标
3. **HairManager** (`hair_manager.py`) - 数据管理

### 数据流

```
AssetItem (拖拽源)
    ↓ (鼠标拖拽)通过QMimeData传输JSON资源数据
    ↓ (放置事件)
ComponentList (放置目标)
    ↓ (创建组件)
HairManager.create_component()
    ↓ (更新UI)
组件添加到列表
```

## 实现细节

### AssetItem (拖拽源)

#### 关键特性:
- **拖拽初始化**: 检测鼠标拖拽距离阈值
- **视觉反馈**: 创建半透明拖拽图像
- **数据传输**: 将资源数据序列化为JSON格式的QMimeData
- **MIME类型**: 支持自定义格式和纯文本

#### 关键方法:
```python
def mousePressEvent(self, event):
    # 存储拖拽起始位置
    
def mouseMoveEvent(self, event):
    # 检查拖拽距离并开始拖拽操作
    
def _start_drag(self):
    # 创建带有资源数据的QDrag
    # 设置自定义MIME类型: "application/x-hair-asset"
    # 创建视觉反馈图像
    
def _create_drag_pixmap(self):
    # 生成半透明控件截图
```

#### MIME数据格式:
- **主要格式**: `application/x-hair-asset` (自定义格式)
- **备用格式**: `text/plain` (JSON字符串)
- **内容**: 序列化为JSON的资源字典

### ComponentList (放置目标)

#### 关键特性:
- **放置接受**: 验证MIME数据格式
- **视觉反馈**: 在拖拽悬停时显示虚线边框
- **数据提取**: 从放置操作解析JSON资源数据
- **组件创建**: 使用HairManager创建组件
- **类型验证**: 当资源类型与标签页类型不匹配时发出警告

#### 关键方法:
```python
def dragEnterEvent(self, event):
    # 检查是否可以接受放置
    # 显示视觉反馈
    
def dragMoveEvent(self, event):
    # 继续拖拽验证
    
def dragLeaveEvent(self, event):
    # 隐藏视觉反馈
    
def dropEvent(self, event):
    # 提取资源数据
    # 创建组件
    # 更新UI
    
def _can_accept_drop(self, event):
    # 验证MIME数据格式
    
def _extract_asset_data(self, event):
    # 从MIME数据解析JSON
    
def _add_component_from_asset(self, asset_data):
    # 使用HairManager创建组件
```

#### 视觉反馈:
- **拖拽进入**: 虚线边框 + 背景高亮
- **拖拽离开**: 移除视觉效果
- **放置成功**: 组件添加到列表

### 错误处理

#### 健壮的错误管理:
1. **JSON解析**: 优雅处理格式错误的数据
2. **MIME类型回退**: 尝试多种数据格式
3. **组件创建**: 在创建前验证资源数据
4. **UI更新**: 处理MListView兼容性问题
5. **日志记录**: 全面的错误日志记录，便于调试

#### 常见错误场景:
- 拖拽数据中的JSON无效
- 缺少资源ID
- 组件创建失败
- UI控件兼容性问题

## 使用示例

### 基本拖拽操作:
1. 用户点击并拖拽AssetItem
2. AssetItem创建带有资源JSON数据的QDrag
3. 视觉反馈显示半透明拖拽图像
4. 用户将项目拖到ComponentList上
5. ComponentList显示虚线边框反馈

### 成功放置:
1. 用户在ComponentList上释放鼠标
2. ComponentList从QMimeData提取资源数据
3. HairManager根据资源创建新组件
4. 组件出现在列表中并被选中
5. 编辑器区域更新显示组件属性

### 类型不匹配处理:
- 如果资源类型与当前标签页不匹配(例如，XGen资源放到插片标签页)
- 记录警告但仍创建组件
- 在保持数据完整性的同时提供灵活性

## 测试

### 测试覆盖范围:
1. **基本功能**: 资源数据的序列化/反序列化
2. **UI组件**: 控件创建和交互
3. **错误处理**: 无效数据和边缘情况
4. **集成**: 端到端拖放工作流

### 测试文件:
- `test_drag_drop_functionality.py` - 全面的测试套件
- 通过UI交互进行手动测试

## 未来增强

### 计划中的改进:
1. **拖拽预览**: 增强拖拽过程中的视觉反馈
2. **放置区域**: 支持具有不同行为的多个放置目标
3. **批量操作**: 同时拖拽多个资源
4. **撤销/重做**: 支持拖放操作的撤销和重做
5. **资源验证**: 放置前验证资源兼容性

### 性能优化:
1. **延迟加载**: 按需加载拖拽图像
2. **缓存**: 缓存常用资源数据
3. **节流**: 限制拖拽事件频率

## 故障排除

### 常见问题:

#### 拖拽未开始:
- 检查鼠标拖拽距离阈值
- 验证AssetItem鼠标事件处理
- 确保Qt应用程序正确初始化

#### 放置未被接受:
- 验证MIME数据格式
- 检查ComponentList放置接受逻辑
- 确保事件处理链正确

#### 组件未创建:
- 检查HairManager.create_component()方法
- 验证模拟数据中是否存在资源ID
- 检查错误日志中的创建失败信息

#### UI未更新:
- 验证信号连接
- 检查MListView兼容性方法
- 确保控件正确刷新

### 调试技巧:
1. 启用调试日志以获取详细的事件跟踪
2. 使用Qt检查器检查控件层次结构
3. 首先使用简化的资源数据进行测试
4. 验证放置事件中的MIME数据内容

## 结论

拖放实现提供了一种健壮、用户友好的方式来从资源创建毛发组件。该架构设计具有可扩展性和可维护性，并包含全面的错误处理和测试覆盖。
