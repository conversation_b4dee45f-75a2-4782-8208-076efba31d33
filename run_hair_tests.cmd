@echo off
setlocal

:: Set the root directory of the project
set "PROJECT_ROOT=%~dp0"

:: Set PYTHONPATH to include the project's root
set "PYTHONPATH=%PROJECT_ROOT%;%PYTHONPATH%"

:: Define the full list of packages for the rez environment.
:: This includes the base package and additional ones for the hair test environment.
set "REZ_PACKAGES=cgame_avatar_factory pytest_qt-4 pydantic-2.5"

:: The test command to be executed, as defined in package.py for '2022_hair'
set "TEST_COMMAND=pytest --cov=cgame_avatar_factory --pyargs cgame_avatar_factory/hair_studio/test"

:: Run the test command within the constructed rez environment.
:: Note the quotes around the package list to ensure it's treated as a single argument.
echo Running tests with packages: %REZ_PACKAGES%
thm +p "%REZ_PACKAGES%" run %TEST_COMMAND%

:: Pause to view the output
pause
