Hair Studio 模块组件引用关系分析
基于对hair_studio模块纯代码文件的分析，我整理出了完整的类图和组件引用关系。以下是详细的分析：

🏗️ 架构层次
Hair Studio模块采用了清晰的分层架构：

数据模型层 (data/)
业务管理层 (manager/)
用户界面层 (ui/)
测试层 (test/)
Maya API层 (maya_api/)
📊 核心组件分析
数据模型层
HairAsset: 毛发素材的数据模型
HairComponent: 毛发组件的数据模型
HairProject: 毛发项目的数据模型
MockHairComponent: 用于开发测试的模拟毛发组件
MockDataManager: 模拟数据管理器，提供测试数据
业务管理层
HairManager: 核心业务逻辑管理器，继承自QtCore.QObject
管理组件的CRUD操作
提供信号机制用于UI通信
集成MockDataManager进行数据管理
用户界面层
HairStudioTab: 主标签页容器，继承自QtWidgets.QTabWidget
BaseHairTab: 基础毛发标签页，包含三个主要区域
ComponentList: 组件列表，支持多选、重排序、右键菜单等增强功能
ComponentItem: 单个组件项的自定义widget
EditorArea: 属性编辑区域，根据毛发类型显示不同属性
AssetLibrary: 素材库，支持搜索和拖拽功能
AssetItem: 单个素材项，支持拖拽操作
🔗 关键引用关系
组合关系 (Composition)
HairStudioTab 包含 3个 BaseHairTab 实例 (card, xgen, curve)
BaseHairTab 包含 EditorArea、ComponentList、AssetLibrary
ComponentList 创建和管理 ComponentItem 实例
AssetLibrary 创建和管理 AssetItem 实例
依赖关系 (Dependency)
所有UI组件都依赖 HairManager 进行业务操作
HairManager 使用 MockDataManager 进行数据管理
所有组件都通过构造函数接收统一的logger实例
数据流关系
AssetItem → ComponentList: 拖拽操作创建组件
ComponentList → EditorArea: 选择组件进行编辑
ComponentList → HairManager: 执行CRUD操作
EditorArea → HairManager: 更新组件属性
🎯 设计特点
统一的Logger管理: 所有组件通过构造函数接收logger实例，避免使用logging.getLogger(__name__)
信号驱动的架构: 大量使用Qt信号机制进行组件间通信
类型特化: 支持三种毛发类型(card, xgen, curve)，每种类型有特定的属性和行为
增强的交互功能: ComponentList支持多选、拖拽重排序、右键菜单等高级功能
模块化设计: 清晰的模块分离，便于维护和扩展
测试友好: 提供MockDataManager和完整的测试框架
这个类图展现了Hair Studio模块完整的组件结构和引用关系，可以帮助理解整个系统的架构设计和数据流向。